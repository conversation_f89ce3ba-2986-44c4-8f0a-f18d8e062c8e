<?php

namespace Tests\Feature;

use App\Models\Conversation;
use App\Models\Message;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class MessageSeenTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $user1;
    protected $user2;
    protected $conversation;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create two test users
        $this->user1 = User::factory()->create([
            'name' => 'Test User 1',
            'email' => '<EMAIL>'
        ]);
        
        $this->user2 = User::factory()->create([
            'name' => 'Test User 2', 
            'email' => '<EMAIL>'
        ]);

        // Create a conversation between the users
        $this->conversation = Conversation::create([
            'type' => 'private',
            'sender_id' => $this->user1->id,
            'receiver_id' => $this->user2->id,
            'title' => 'Test Conversation',
            'last_message_at' => now(),
            'is_active' => true
        ]);
    }

    /** @test */
    public function it_can_mark_messages_as_seen()
    {
        // Create some messages from user1 to user2
        $message1 = Message::create([
            'conversation_id' => $this->conversation->id,
            'sender_id' => $this->user1->id,
            'content' => 'Hello there!',
            'message_type' => 'text',
            'read_at' => null
        ]);

        $message2 = Message::create([
            'conversation_id' => $this->conversation->id,
            'sender_id' => $this->user1->id,
            'content' => 'How are you?',
            'message_type' => 'text',
            'read_at' => null
        ]);

        // Verify messages are initially unread
        $this->assertNull($message1->fresh()->read_at);
        $this->assertNull($message2->fresh()->read_at);

        // Act as user2 and mark messages as seen
        $response = $this->actingAs($this->user2)
            ->post(route('message.mark_seen', $this->conversation->ids));

        // Assert the response is successful
        $response->assertStatus(200);
        $response->assertJson([
            'status' => true,
            'message' => 'Messages marked as seen',
            'updated_count' => 2
        ]);

        // Verify messages are now marked as read
        $this->assertNotNull($message1->fresh()->read_at);
        $this->assertNotNull($message2->fresh()->read_at);
    }

    /** @test */
    public function it_only_marks_messages_from_other_users_as_seen()
    {
        // Create messages from both users
        $messageFromUser1 = Message::create([
            'conversation_id' => $this->conversation->id,
            'sender_id' => $this->user1->id,
            'content' => 'Message from user 1',
            'message_type' => 'text',
            'read_at' => null
        ]);

        $messageFromUser2 = Message::create([
            'conversation_id' => $this->conversation->id,
            'sender_id' => $this->user2->id,
            'content' => 'Message from user 2',
            'message_type' => 'text',
            'read_at' => null
        ]);

        // Act as user2 and mark messages as seen
        $response = $this->actingAs($this->user2)
            ->post(route('message.mark_seen', $this->conversation->ids));

        // Assert only 1 message was marked as seen (the one from user1)
        $response->assertJson([
            'status' => true,
            'updated_count' => 1
        ]);

        // Verify only the message from user1 is marked as read
        $this->assertNotNull($messageFromUser1->fresh()->read_at);
        $this->assertNull($messageFromUser2->fresh()->read_at);
    }

    /** @test */
    public function it_does_not_mark_already_read_messages()
    {
        // Create a message that's already read
        $readMessage = Message::create([
            'conversation_id' => $this->conversation->id,
            'sender_id' => $this->user1->id,
            'content' => 'Already read message',
            'message_type' => 'text',
            'read_at' => now()->subMinutes(5)
        ]);

        // Create an unread message
        $unreadMessage = Message::create([
            'conversation_id' => $this->conversation->id,
            'sender_id' => $this->user1->id,
            'content' => 'Unread message',
            'message_type' => 'text',
            'read_at' => null
        ]);

        // Act as user2 and mark messages as seen
        $response = $this->actingAs($this->user2)
            ->post(route('message.mark_seen', $this->conversation->ids));

        // Assert only 1 message was updated (the unread one)
        $response->assertJson([
            'status' => true,
            'updated_count' => 1
        ]);

        // Verify the unread message is now read
        $this->assertNotNull($unreadMessage->fresh()->read_at);
        
        // Verify the already read message timestamp didn't change
        $this->assertEquals(
            $readMessage->read_at->format('Y-m-d H:i:s'),
            $readMessage->fresh()->read_at->format('Y-m-d H:i:s')
        );
    }
}
