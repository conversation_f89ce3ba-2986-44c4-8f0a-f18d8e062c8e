@php
    $previousDate = null;
    $today = \Carbon\Carbon::today();
    $yesterday = \Carbon\Carbon::yesterday();
@endphp

@foreach ($messages as $message)
    @php
        $messageDate = \Carbon\Carbon::parse($message->created_at);
        $currentDateString = $messageDate->format('Y-m-d');

        // Format date display
        if ($messageDate->isSameDay($today)) {
            $dateDisplay = 'Today';
        } elseif ($messageDate->isSameDay($yesterday)) {
            $dateDisplay = 'Yesterday';
        } else {
            $dateDisplay = $messageDate->format('M j, Y');
        }
    @endphp

    {{-- Show date separator when date changes --}}
    @if ($previousDate !== $currentDateString)
        <div class="chat_date_time">
            <p>{{ $dateDisplay }}</p>
        </div>
        @php
            $previousDate = $currentDateString;
        @endphp
    @endif

    @if ($message->sender_id == auth()->id())
        <div class="msg msg_right">
            <div class="chat_bubble">
                @if ($message->message_type === 'text')
                    <p class="">{{ htmlspecialchars_decode($message->content, ENT_QUOTES) }}</p>
                @elseif ($message->message_type === 'image' && $message->attachment)
                    @php
                        $attachment = json_decode($message->attachment, true);
                    @endphp
                    <div class="message-attachment">
                        <img src="{{ $attachment['file_url'] }}" alt="{{ $attachment['original_name'] }}"
                             style="max-width: 200px; max-height: 200px; border-radius: 8px; cursor: pointer;"
                             onclick="window.open('{{ $attachment['file_url'] }}', '_blank')">
                        @if($message->content && $message->content !== 'Sent an image')
                            <p class="">{{ htmlspecialchars_decode($message->content, ENT_QUOTES) }}</p>
                        @endif
                    </div>
                @else
                    <p class="">{{ htmlspecialchars_decode($message->content, ENT_QUOTES) }}</p>
                @endif
                <span class="message-time">
                    {{ $messageDate->format('g:i A') }}
                    {{-- Only show seen indicators for messages sent by current user --}}
                    @if($message->sender_id == auth()->id())
                        @if($message->read_at)
                            <i class="fas fa-check-double seen-indicator" style="color: #4CAF50; margin-left: 5px;" title="Seen"></i>
                        @else
                            <i class="fas fa-check" style="color: #999; margin-left: 5px;" title="Sent"></i>
                        @endif
                    @endif
                </span>
                @if($message->seen_by_user)
                    <div class="message-seen-status">
                        <small class="text-muted">Seen by {{ $message->seen_by_user->name }}</small>
                    </div>
                @endif
            </div>
        </div>
    @else
        <div class="msg msg_left">
            <div class="chat_bubble">
                @if ($message->message_type === 'text')
                    <p class="">{{ htmlspecialchars_decode($message->content, ENT_QUOTES) }}</p>
                @elseif ($message->message_type === 'image' && $message->attachment)
                    @php
                        $attachment = json_decode($message->attachment, true);
                    @endphp
                    <div class="message-attachment">
                        <img src="{{ $attachment['file_url'] }}" alt="{{ $attachment['original_name'] }}"
                             style="max-width: 200px; max-height: 200px; border-radius: 8px; cursor: pointer;"
                             onclick="window.open('{{ $attachment['file_url'] }}', '_blank')">
                        @if($message->content && $message->content !== 'Sent an image')
                            <p class="">{{ htmlspecialchars_decode($message->content, ENT_QUOTES) }}</p>
                        @endif
                    </div>
                @else
                    <p class="">{{ htmlspecialchars_decode($message->content, ENT_QUOTES) }}</p>
                @endif
                <span class="message-time">{{ $messageDate->format('g:i A') }}</span>
                @if($message->seen_by_user)
                    <div class="message-seen-status">
                        <small class="text-muted">Seen by {{ $message->seen_by_user->name }}</small>
                    </div>
                @endif
            </div>
        </div>
    @endif
@endforeach
