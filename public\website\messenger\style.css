footer {
    display: none
}

html {
    overflow-y: hidden;
}

.conversation-user {
    background: none;
    border: none;
    box-shadow: none;
    cursor: pointer;
}

/* Chat date and time styling */
.messenger_main_sec .messenger_wrapper .chat_body .chat_date_time {
    text-align: center;
    margin: 15px 0;
}

.messenger_main_sec .messenger_wrapper .chat_body .chat_date_time p {
    background-color: rgba(0, 0, 0, 0.1);
    color: #666;
    padding: 5px 12px;
    border-radius: 15px;
    display: inline-block;
    font-size: 12px;
    font-weight: 500;
    margin: 0;
}

.chat_bubble {
    text-align: left;
}

/* Message time styling */
.messenger_main_sec .messenger_wrapper .chat_body .msg .chat_bubble .message-time {
    display: block;
    font-size: 10px;
    color: rgba(0, 0, 0, 0.5);
    text-align: right;
    margin-top: 5px;
    font-weight: 400;
}

.messenger_main_sec .messenger_wrapper .chat_body .msg_left .chat_bubble .message-time {
    color: rgba(0, 0, 0, 0.6);
}

.messenger_main_sec .messenger_wrapper .chat_body .msg_right .chat_bubble .message-time {
    color: rgba(0, 0, 0, 0.7);
}

/* Message seen status styling */
.messenger_main_sec .messenger_wrapper .chat_body .msg .chat_bubble .message-seen-status {
    margin-top: 2px;
    text-align: right;
}

.messenger_main_sec .messenger_wrapper .chat_body .msg .chat_bubble .message-seen-status small {
    font-size: 9px;
    color: rgba(0, 0, 0, 0.5);
    font-style: italic;
}

.messenger_main_sec .messenger_wrapper .chat_body .msg_left .chat_bubble .message-seen-status small {
    color: rgba(0, 0, 0, 0.6);
}

.messenger_main_sec .messenger_wrapper .chat_body .msg_right .chat_bubble .message-seen-status small {
    color: rgba(0, 0, 0, 0.7);
}

/* Seen indicator styling */
.seen-indicator {
    color: #4CAF50 !important;
    font-size: 10px;
}

.fa-check {
    color: #999 !important;
    font-size: 10px;
}

.fa-clock {
    color: #999 !important;
    font-size: 10px;
}

.active-conversation {
    background-color: #ffce32;
}

/* Attachment button styles */
.attachment_button {
    display: inline-block;
    margin-left: 10px;
}

.attachment_button .btn {
    background: none;
    border: none;
    color: #666;
    font-size: 18px;
    padding: 5px;
    cursor: pointer;
}

.attachment_button .btn:hover {
    color: #333;
}

/* Image upload styles */
.message-attachment {
    margin-bottom: 8px;
}

.message-attachment img {
    border-radius: 8px;
    max-width: 100%;
    height: auto;
    cursor: pointer;
    transition: opacity 0.2s;
}

.message-attachment img:hover {
    opacity: 0.9;
}

.message-attachment video {
    border-radius: 8px;
    max-width: 100%;
    height: auto;
}

.file-attachment .file-info {
    background: #f8f9fa !important;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 12px;
    display: flex;
    align-items: center;
    transition: background-color 0.2s;
}

.file-attachment .file-info:hover {
    background: #e9ecef !important;
}

.file-attachment .file-info i {
    font-size: 24px;
    margin-right: 12px;
    color: #6c757d;
}

.file-attachment .file-info a {
    text-decoration: none;
    color: #007bff;
    font-weight: 500;
}

.file-attachment .file-info a:hover {
    text-decoration: underline;
}

.send_msg_btn.file-selected {
    background-color: #28a745;
    color: white;
}

/* Dropdown menu styling */
.dropdown-menu {
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.dropdown-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 16px;
}

.dropdown-item:hover {
    background-color: #f8f9fa;
}

.dropdown-item i {
    color: #6c757d;
}

/* Loading indicator for scroll-up pagination */
.load-more-messages-indicator {
    padding: 10px 0;
    text-align: center;
    background: rgba(0, 0, 0, 0.05);
    border-radius: 8px;
    margin: 5px 0;
    transition: opacity 0.3s ease;
}

.load-more-messages-indicator .spinner-border-sm {
    width: 1rem;
    height: 1rem;
    border-width: 0.1em;
}

.load-more-messages-indicator small {
    color: #6c757d;
    font-size: 0.8rem;
}

/* Smooth scrolling for chat container */
.chat_body.scrollable-section-2 {
    scroll-behavior: auto;
    /* Disable smooth scrolling for better pagination performance */
}

/* Message loading animation */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(10px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.msg {
    animation: fadeInUp 0.3s ease-out;
}

/* Unread conversation styles */
.single_chat_thread.has-unread {
    background-color: rgba(255, 206, 50, 0.1);
    border-left: 3px solid #ffce32;
}

.unread-text {
    font-weight: bold !important;
    color: #000 !important;
}

.user_profile_picture {
    position: relative;
    overflow: visible !important;
}

.unread-count-badge {
    position: absolute;
    top: -5px;
    right: -5px;
    min-width: 20px;
    height: 20px;
    background-color: #ff4444;
    color: white;
    border-radius: 50%;
    border: 2px solid white;
    z-index: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 11px;
    font-weight: bold;
    line-height: 1;
}

.unread-count-badge:empty {
    display: none;
}

/* Unread count in date area */
.chat_date_unread_wrapper {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 2px;
}

.unread-count-text {
    background-color: #ff4444;
    color: white;
    border-radius: 12px;
    padding: 2px 8px;
    min-width: 20px;
    text-align: center;
}

.unread-count-number {
    font-size: 11px;
    font-weight: bold;
    line-height: 1;
}

/* Hover effects for unread conversations */
.single_chat_thread.has-unread:hover {
    background-color: rgba(255, 206, 50, 0.2);
}

.chat_inner_search {
    /* position: absolute; */
    display: none;
    opacity: 0;
    transform: translateX(-20px);
    transition: all 0.3s ease;
    z-index: 1000;
    background: #fff;
    /* box-shadow: 0 2px 10px rgba(0, 0, 0, 0.15); */
    /* padding: 10px; */
    width: 300px;
    /* Adjust as needed */
    flex: 1
}

.chat_inner_search.show {
    opacity: 1;
    transform: translateX(0);
}

/* Role badge styles */
.user-role-badge {
    font-size: 10px;
    padding: 2px 6px;
    border-radius: 10px;
    margin-left: 5px;
    font-weight: 500;
    text-transform: uppercase;
}

.user-role-badge.role-provider {
    background-color: #28a745;
    color: white;
}

.user-role-badge.role-traveller {
    background-color: #007bff;
    color: white;
}

.user-role-badge.role-admin {
    background-color: #dc3545;
    color: white;
}

.user-role-badge.role-user {
    background-color: #6c757d;
    color: white;
}

/* Conversation thread role-based styling */
/* .single_chat_thread.role-provider {
            border-left: 3px solid #28a745;
        } */

/* .single_chat_thread.role-traveller {
            border-left: 3px solid #007bff;
        } */

/* .single_chat_thread.role-admin {
            border-left: 3px solid #dc3545;
        } */

/* .single_chat_thread.role-user {
            border-left: 3px solid #6c757d;
        } */

/* Emoji Picker Styles */
.emoji-picker {
    position: absolute;
    bottom: 70px;
    right: 20px;
    background: white;
    border: 1px solid #ddd;
    border-radius: 10px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    padding: 15px;
    width: 520px;
    max-height: 300px;
    overflow-y: auto;
    z-index: 1000;
    display: none;
}

.emoji-picker.show {
    display: block;
}

.emoji-categories {
    display: flex;
    justify-content: space-around;
    margin-bottom: 10px;
    border-bottom: 1px solid #eee;
    padding-bottom: 10px;
}

.emoji-category {
    padding: 5px 10px;
    cursor: pointer;
    border-radius: 5px;
    transition: background-color 0.2s;
}

.emoji-category:hover,
.emoji-category.active {
    background-color: #f0f0f0;
}

.emoji-grid {
    display: grid;
    grid-template-columns: repeat(8, 1fr);
    gap: 5px;
    max-height: 200px;
    overflow-y: auto;
}

.emoji-item {
    font-size: 20px;
    padding: 8px;
    text-align: center;
    cursor: pointer;
    border-radius: 5px;
    transition: background-color 0.2s;
}

.emoji-item:hover {
    background-color: #f0f0f0;
}

.attachment_button {
    display: flex;
    gap: 5px;
}

.attachment_button .btn {
    padding: 8px 12px;
    border: none;
    background: transparent;
    color: #666;
    transition: color 0.2s;
}

.attachment_button .btn:hover {
    color: #007bff;
}

.attachment_button .btn.active {
    color: #007bff;
}

/* Responsive design for emoji picker */
@media (max-width: 768px) {
    .emoji-picker {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        width: 100%;
        max-height: 50vh;
        border-radius: 15px 15px 0 0;
        border: none;
        border-top: 1px solid #ddd;
    }

    .emoji-grid {
        grid-template-columns: repeat(6, 1fr);
    }
}

/* Smooth animations */
.emoji-picker {
    transition: all 0.3s ease;
    transform: translateY(10px);
    opacity: 0;
}

.emoji-picker.show {
    transform: translateY(0);
    opacity: 1;
}

/* Custom scrollbar for emoji grid */
.emoji-grid::-webkit-scrollbar {
    width: 6px;
}

.emoji-grid::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.emoji-grid::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.emoji-grid::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* =============================================
   TYPING INDICATOR STYLES
   ============================================= */

.typing-indicator-container {
    padding: 10px 20px;
    background: transparent;
    border-top: 1px solid #e0e0e0;
}

.typing-indicator {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 8px 12px;
    background: #f5f5f5;
    border-radius: 20px;
    max-width: fit-content;
    animation: fadeIn 0.3s ease-in-out;
}

.typing-dots {
    display: flex;
    gap: 3px;
    align-items: center;
}

.typing-dots span {
    width: 6px;
    height: 6px;
    background: #999;
    border-radius: 50%;
    animation: typingDot 1.4s infinite ease-in-out;
}

.typing-dots span:nth-child(1) {
    animation-delay: 0s;
}

.typing-dots span:nth-child(2) {
    animation-delay: 0.2s;
}

.typing-dots span:nth-child(3) {
    animation-delay: 0.4s;
}

.typing-text {
    font-size: 12px;
    color: #666;
    font-style: italic;
}

@keyframes typingDot {
    0%, 60%, 100% {
        transform: scale(1);
        opacity: 0.5;
    }
    30% {
        transform: scale(1.2);
        opacity: 1;
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Dark mode support for typing indicator */
.dark-mode .typing-indicator {
    background: #2a2a2a;
}

.dark-mode .typing-text {
    color: #ccc;
}

.dark-mode .typing-dots span {
    background: #ccc;
}

.dark-mode .typing-indicator-container {
    border-top-color: #333;
}

/* Message Search Styles */
.search-highlight {
    background-color: #ffeb3b !important;
    color: #000 !important;
    padding: 2px 4px;
    border-radius: 3px;
    font-weight: bold;
}

.current-search-result {
    background-color: #ff9800 !important;
    color: #fff !important;
    animation: pulse-search 1s ease-in-out;
}

@keyframes pulse-search {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.chat_inner_search {
    display: none;
    background: #f8f9fa;
    border-radius: 8px;
    padding: 10px;
    margin: 5px 0;
    border: 1px solid #e9ecef;
}

.search_buttons_wrapper {
    display: flex;
    align-items: center;
    gap: 5px;
}

.search_buttons_wrapper input {
    flex: 1;
    border: 1px solid #ddd;
    border-radius: 20px;
    padding: 8px 15px;
    font-size: 14px;
}

.search_buttons_wrapper input:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.search_above, .search_below {
    background: #007bff;
    color: white;
    border: none;
    border-radius: 50%;
    width: 35px;
    height: 35px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    outline: none;
}

.search_above:hover:not(:disabled), .search_below:hover:not(:disabled) {
    background: #0056b3;
    transform: scale(1.05);
}

.search_above:disabled, .search_below:disabled {
    background: #6c757d !important;
    cursor: not-allowed;
    transform: none;
    opacity: 0.6;
}

.search_above:focus, .search_below:focus {
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.find_messages_btn {
    background: transparent;
    border: none;
    color: #6c757d;
    font-size: 18px;
    padding: 8px;
    border-radius: 50%;
    transition: all 0.2s ease;
}

.find_messages_btn:hover {
    background: #f8f9fa;
    color: #007bff;
}

/* Search results counter (optional enhancement) */
.search-results-counter {
    font-size: 12px;
    color: #6c757d;
    margin-left: 10px;
    white-space: nowrap;
}

/* Search results display */
.search-results-header {
    position: sticky;
    top: 0;
    z-index: 10;
    background: #f8f9fa !important;
    border: 1px solid #e9ecef;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.search-result-message {
    margin: 10px 0;
    opacity: 0.95;
}

.search-result-message .chat_bubble {
    border: 1px solid #e9ecef;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.search-result-date {
    font-style: italic;
    border-top: 1px solid rgba(0,0,0,0.1);
    padding-top: 3px;
    margin-top: 5px !important;
}

.search-result-message.current-result {
    transform: scale(1.02);
    transition: transform 0.2s ease;
}

.search-result-message.current-result .chat_bubble {
    border-color: #007bff;
    box-shadow: 0 2px 8px rgba(0,123,255,0.3);
}